package main

import (
	"fmt"
	"log"
	"net"
	pb "proco/relay"
	"google.golang.org/grpc"
	_ "google.golang.org/grpc/reflection"
)

func main() {
	port := 50051
	listenport, err := net.Listen("tcp", fmt.Sprintf(":%d", port))
	if err != nil {
		log.Fatalf("cannot listen port : %v", err)
	}

	server := grpc.NewServer()

	pb.RegisterGreeterServer(server,)





	err = listenport.Close()
	if err != nil {
		log.Fatalf("portは開放されていない可能性があります : %v", err)
	}

	fmt.Println("sample")
}
